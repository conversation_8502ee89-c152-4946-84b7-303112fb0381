export default `flf2a$ 5 5 10 -1 53
by <PERSON><PERSON><PERSON> [http://www.pthbb.org/software/figlet/tsalagi.html]
   11/97, 2/02, 8/02
Permission to modify granted, but please send me a copy or notify me.
Also available: best fit/match of characters, allow punctuation like . " ' ?
          _______________________________________________________
         (                                                       )
         (                     __   _ _  _ __  __                )
         (                    / _'_ | |  |  \\  /                 )
         (                   |      | |  |   \\/                  )
         (                   |   @  \\ \\  /   /                   )
         (                    \\__/   \\/\\/   @                    )
         (                                                       )
         (               Syllabry Conversion Table               )
         (                                                       )
         (      a, a   e, \`    i, ~    o, q    u, Q    v, A      )
         (     ------+-------+--------+-------+------+------     ) There is
         (     ga, s                                             ) rhyme and
         (     ka, n   ge, 1   gi, !   go, w   gu, W   gv, S     ) reason
         (     ------+-------+--------+-------+------+------     )
         (     ha, d   he, 2   hi, @   ho, e   hu, E   hv, D     ) a \` ~ q Q A
         (     ------+-------+--------+-------+------+------     ) s
         (     la, f   le, 3   li, #   lo, r   lu, R   lv, F     ) n 1 ! w W S
         (     ------+-------+--------+-------+------+------     ) d 2 @ e E D
         (     ma, g   me, 4   mi, $   mo, t   mu, T             ) f 3 # r R F
         (     ------+-------+--------+-------+------+------     ) g 4 $ t T
         (     na, h                                             ) h
         (     hna, m                                            ) m
         (     nah, X  ne, 5   ni, %   no, y   nu, Y   nv, G     ) X 5 % y Y G
         (     ------+-------+--------+-------+------+------     ) j 6 ^ u U H
         (     qua, j  que, 6  qui, ^  quo, u  quu, U  quv, H    ) k
         (     ------+-------+--------+-------+------+------     ) C 7 & i I J
         (     sa, k                                             ) l 8 * o O K
         (     s, C    se, 7   si, &   so, i   su, I   sv, J     ) / 9 (
         (     ------+-------+--------+-------+------+------     ) V
         (     da, l   de, 8   di, *   do, o   du, O   dv, K     ) Z 0 ) p P L
         (     ta, /   te, 9   ti, (                             ) B < _ [ { N
         (     ------+-------+--------+-------+------+------     ) v > + ] } M
         (     dla, V                                            ) b \\ | z x c
         (     tla, Z  tle, 0  tli, )  tlo, p  tlu, P  tlv, L    )
         (     ------+-------+--------+-------+------+------     )
         (     tsa, B  tse, <  tsi, _  tso, [  tsu, {  tsv, N    )
         (     ------+-------+--------+-------+------+------     )
         (     wa, v   we, >   wi, +   wo, ]   wu, }   wv, M     )
         (     ------+-------+--------+-------+------+------     )
         (     ya, b   ye, \\   yi, |   yo, z   yu, x   yv, c     )
         (                                                       )
         (_______________________________________________________)

    This uses the mapping of Joan Touzet's Freeware Cherokee font v1.02

        font:   http://www.atypical.net//Cherokee.html
        keymap: http://www.atypical.net//CherTabl.html

$$     #
$$     #
$$     #
$$     #
$$     ##
__  __ #
 \\  /  #
  \\/   #
  /    #
 @     ##
  | |  #
  \` '  #
       #
       #
       ##
  __   #
 |  |  #
 | \`'  #
 |     #
_|_    ##
_  _   #
|  |   #
|__|   #
|  |   #
|  |   ##
___    #
 |     #
 |__   #
 |  |  #
_|  |_ ##
___    #
 |     #
 |_    #
 | \\   #
_|_/   ##
  | |  #
   ^   #
       #
       #
       ##
  ___  #
 | |   #
   |   #
  /|   #
@/ |_  ##
  __,  #
 /  '  #
|      #
|      #
 \\__/  ##
  ___  #
   |   #
   |   #
  /|   #
@/ |_  ##
  _    #
 / \\   #
|~| |  #
| |_|  #
 \\_/   ##
       #
       #
       #
 |     #
/      ##
       #
       #
 ---   #
       #
       ##
       #
       #
       #
/\\     #
\\/     ##
       #
| | | |#
| | | |#
|  \\  /#
 \\/ \\/ ##
___    #
 |     #
 |     #
 |     #
_|__|  ##
 _____ #
   |   #
   |/u #
   |   #
 __|__ ##
 ____  #
\`___ \\ #
  |  | #
  |--' #
 _|_   ##
    /\\ #
  _|  @#
 / |   #
|  |   #
 \\_|   ##
  _    #
 / \\ | #
|   \\| #
|   /| #
 \\_/ | ##
       #
  /\\   #
 |  \\  #
 |   \\ #
@|  _|_##
       #
   __  #
  /  \\ #
     | #
(_|_/  ##
       #
 /|    #
/_|_,  #
  | '  #
 _|_   ##
  __   #
 /  '  #
 \\_    #
   \\   #
\`__/   ##
 ____  #
| |    #
  | _  #
  |  \\ #
   \\_/ ##
/\\     #
\\/     #
       #
/\\     #
\\/     ##
/\\     #
\\/     #
       #
 |     #
/      ##
 ___   #
| |  | #
  |  / #
  | /  #
  |/   ##
       #
       #
  ---  #
  ---  #
       ##
     /\\#
  /\\ \`|#
/ | | |#
| |/  |#
 \\|__/ ##
 /~~\\  #
     | #
   ~~  #
  /\\   #
  \\/   ##
   _   #
  / \\  #
  |_|  #
  | |  #
@/ @/  ##
  _    #
 <_>   #
       #
  |    #
 _|_   ##
  __   #
 / _'_ #
|      #
|   @  #
 \\__/  ##
   __  #
  /  \\ #
  _   |#
/\\|   |#
\\/ \\_/ ##
       #
   /\\  #
  | |  #
| | | _#
 \\_\\'_/##
 ____  #
  |  | #
  |    #
  |    #
 _|_   ##
  _    #
 \` |   #
|-_|   #
   |   #
  _|_  ##
  __   #
 /  \\  #
|    |"#
|    | #
 \\__/  ##
 __    #
/  '   #
\\_     #
/  _   #
\\__,   ##
      _ #
  () / '#
 /~\\    #
|   |   #
 \\_/    ##
_____  #
 |  \\  #
 |__/  #
 | \\   #
_|  \\_ ##
     _ #
  /\\/ '#
 /\\    #
|  |   #
 \\/    ##
____   #
 |  \\  #
 |__/  #
 |     #
.|__   ##
  _    #
 / '   #
| __   #
|'  \\  #
 \\__/  ##
  __   #
 /  ~~ #
|      #
|    ~ #
 \\__/  ##
  __   #
 /  |  #
 \\_    #
   \\   #
|__/   ##
    _  #
 /\\/ \\ #
_| \\ ' #
  /|   #
  \\/   ##
  _    #
 / \\   #
|   |) #
|   |  #
 \\_/   ##
     _ #
 |\\ /| #
 | | | #
 |   | #
.|   |_##
       #
 |--\\  #
 |_    #
 |     #
 |--/  ##
    __ #
 \\  /  #
  \\/   #
_ /    #
\\/     ##
    _  #
 / \` \\ #
|  _  |#
| | ' |#
 \\_\\_/ ##
  /\\   #
  \\/\\_ #
 /  / \\#
/\\  | |#
\\/  \\_/##
   ___ #
    |  #
    |  #
 .  |  #
  \\_/  ##
  __,  #
 /  '  #
|      #
|  -+- #
 \\__|  ##
  _    #
 / |   #
 _ |   #
 \`-|   #
  _|_  ##
  _    #
 | '   #
 |     #
 |     #
_|__|  ##
___ _  #
 | /   #
 |/\\   #
 |  \\  #
_|_ _\\_##
  __   #
 |  |  #
 |-<   #
 |  |  #
>|__'  ##
  _  _ #
 /| \` \\#
|/    |#
|     |#
 \\___/ ##
 _     #
/ \\/\\  #
  /\\'  #
 |  |  #
  \\/   ##
___    #
 |     #
 |     #
 |/|'  #
_| |   ##
 ___   #
 |  \\  #
 |__/  #
 | \\   #
 |  \\  ##
  __   #
 |  \\  #
 |   \\ #
 |   / #
 |__/  ##
   __  #
  \`  \\ #
   _  |#
|  |  |#
 \\_|_/ ##
____   #
 |  >  #
 |-<   #
 |  \\  #
_|__/  ##
 ___   #
  |    #
(>|__  #
  | /  #
  |/   ##
 ___   #
  |    #
  |_,  #
  | '  #
 _|_   ##
_ _  _ #
| |  | #
| |  | #
\\ \\  / #
 \\/\\/  ##
  ___  #
   |_-_#
\`\\/\\   #
 |  |  #
  \\/   ##
  __   #
 /  \\  #
|____| #
|    | #
 \\__/  ##
 ___   #
  |    #
 .|()  #
()|'   #
 _|_   ##
 ___   #
| | |  #
  |    #
  |    #
|_|_|  ##
___  _ #
 |   | #
 |---| #
 |   | #
  \\_/  ##
 ___   #
  |    #
  |    #
  |  . #
  \`__' ##
-|-    #
 |     #
 |     #
 | _   #
  _|   ##
  __   #
 \`  \\  #
     | #
|)  /  #
 \\_/   ##
___  __#
 |    |#
 \\    |#
  \\  / #
   \\/  ##
  ___  #
   |   #
(|_|_  #
 | |   #
 \`_'   ##
  ___  #
   |__ #
 _/   \\#
/ \\   |#
\\ / \`_/##
  __   #
 /  '  #
| ,_   #
| ' |  #
 \\__/  ##
   __  #
  /  \\ #
__\\___ #
    \\  #
 \\__/  ##
 ___   #
 / \\   #
 \`  |  #
    |  #
 \\_/   ##
       #
 ,  |~,#
@|  |  #
 |  /  #
  \\/   ##
  __   #
 /  '  #
|   _  #
|   |  #
 \\_/|\` ##
       #
       #
   /|  #
  /_|  #
_/   \\_##
  __   #
 / '~  #
|      #
|  _   #
 \\__|  ##
 ____  #
|  /   #
  /    #
 /     #
/___|  ##
   _   #
  / '  #
 /_    #
 | |   #
_| |   ##
  ___  #
   |   #
  _|   #
 / |   #
 \\_'   ##
   ___ #
    |  #
   / \\ #
  /   |#
\\/ ('/ ##
  _    #
 / \\   #
|  |   #
 \\ |   #
@_/    ##
 _____ #
|  |  |#
   |   #
   |   #
  _|_  ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
       #
       #
       #
       #
�      ##
`