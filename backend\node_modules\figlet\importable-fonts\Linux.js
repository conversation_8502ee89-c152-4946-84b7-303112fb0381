export default `flf2a$ 4 3 9 -1 13
linux by <PERSON> 5/12/95 - based on .sig of <PERSON><PERSON>.
Figlet release 2.0 -- August 5, 1993

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
     - the "hardblank" -- prints as a blank, but can't be smushed
4    - height of a character
3    - height of a character, not including descenders
9    - max line length (excluding comment lines) + a fudge factor
-1   - default smushmode for this font (like "-m 15" on command line)
13   - number of comment lines

$@
$@
$@
$@@
.-.@
\`v'@
 o @
   @@
||@
  @
  @
  @@
 || @
=  =@
 || @
    @@
.-|-.@
 \\|\\ @
\`-|-'@
     @@
o ,-,@
 / / @
\`-' o@
     @@
a  @
 n @
  d@
   @@
|@
 @
 @
 @@
 ,-,@
( ( @
 \`-\`@
    @@
.-. @
 ) )@
'-' @
    @@
\\|/@
-*-@
/|\\@
   @@
 # @
###@
 # @
   @@
 @
 @
/@
 @@
   @
###@
   @
   @@
 @
 @
O@
 @@
  .-.@
 / / @
'-'  @
     @@
.----.@
| || |@
\`----'@
      @@
 /.@
 ||@
 --@
   @@
.---,@
 / / @
\`---'@
     @@
.---.@
  -||@
\`---'@
     @@
 ,--.@
'-| |@
  \`-'@
     @@
,---@
 \\ \\@
\`--'@
    @@
,-.  @
| /-.@
\`---'@
     @@
.---.@
\`-/ /@
 \`-' @
     @@
.--.@
\`//.@
\`--'@
    @@
.--.@
\`/ /@
\`-' @
    @@
 $@
o$@
o$@
 $@@
 $@
o$@
/$@
 $@@
$ __$@
$/ /$@
$\`-\`$@
$   $@@
$___$@
$---$@
$   $@
     @@
$__ $@
$\\ \\$@
$'-'$@
$   $@@
,--.@
\`// @
 o  @
    @@
e @
 a@
  @
  @@
.---.@
| | |@
\`-^-'@
     @@
.--.@
|-< @
\`--'@
    @@
.---.@
| |  @
\`---'@
     @@
.--. @
| \\ \\@
\`-'-'@
     @@
.---.@
| |- @
\`---'@
     @@
.---.@
| |- @
\`-'  @
     @@
.---.@
| |'_@
\`-'-/@
     @@
.-. .-.@
| |=| |@
\`-' \`-'@
       @@
.-.@
| |@
\`-'@
   @@
  .-.@
 ,| |@
\`---'@
     @@
.-.,-.@
| . < @
\`-'\`-'@
      @@
.-.   @
| |__ @
\`----'@
      @@
.-.-.-.@
| | | |@
\`-'-'-'@
       @@
.-..-.@
| .\` |@
\`-'\`-'@
      @@
.----.@
| || |@
\`----'@
      @@
.---.@
| |-'@
\`-'  @
     @@
.---.@
| O ,@
\`-'\\\\@
     @@
.---. @
| |-< @
\`-'\`-'@
      @@
.---.@
 \\ \\ @
\`---'@
     @@
.---.@
\`| |'@
 \`-' @
     @@
.-..-.@
| || |@
\`----'@
      @@
.-..-.@
 \\  / @
  \`'  @
      @@
.-.-.-.@
| | | |@
\`-----'@
       @@
.-..-.@
 >  < @
'-'\`-\`@
      @@
.-..-.@
 >  / @
 \`-'  @
      @@
.---,@
 / / @
\`---'@
     @@
,,-@
|| @
\`\`-@
   @@
.-.  @
 \\ \\ @
  \`-'@
     @@
-..@
 ||@
-''@
   @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
.---.@
| | |@
\`-^-'@
     @@
.--.@
|-< @
\`--'@
    @@
.---.@
| |  @
\`---'@
     @@
.--. @
| \\ \\@
\`-'-'@
     @@
.---.@
| |- @
\`---'@
     @@
.---.@
| |- @
\`-'  @
     @@
.---.@
| |'_@
\`-'-/@
     @@
.-. .-.@
| |=| |@
\`-' \`-'@
       @@
.-.@
| |@
\`-'@
   @@
  .-.@
 ,| |@
\`---'@
     @@
.-.,-.@
| . < @
\`-'\`-'@
      @@
.-.   @
| |__ @
\`----'@
      @@
.-.-.-.@
| | | |@
\`-'-'-'@
       @@
.-..-.@
| .\` |@
\`-'\`-'@
      @@
.----.@
| || |@
\`----'@
      @@
.---.@
| |-'@
\`-'  @
     @@
.---.@
| O ,@
\`-'\\\\@
     @@
.---. @
| |-< @
\`-'\`-'@
      @@
.---.@
 \\ \\ @
\`---'@
     @@
.---.@
\`| |'@
 \`-' @
     @@
.-..-.@
| || |@
\`----'@
      @@
.-..-.@
 \\  / @
  \`'  @
      @@
.-.-.-.@
| | | |@
\`-----'@
       @@
.-..-.@
 >  < @
'-'\`-\`@
      @@
.-..-.@
 >  / @
 \`-'  @
      @@
.---,@
 / / @
\`---'@
     @@
 /@
{ @
 \\@
  @@
.-.@
| |@
| |@
\`-'@@
\\ @
 }@
/ @
  @@
     @
,-._,@
     @
     @@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
`