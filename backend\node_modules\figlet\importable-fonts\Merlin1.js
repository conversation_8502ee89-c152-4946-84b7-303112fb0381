export default `flf2a$ 8 7 16 0 24 0 64 0
Author : <PERSON><PERSON>
Date   : 2005/5/7 18:52:57
Version: 1.0
---------------------------------------------------------------
  ___      ___   _______   _______   ___      __   _____  ___
 ("  \\    /"  | /"      | /"      \\ |"  |    |" \\ (\\"   \\|"  \\
  \\   \\  //   |(: ______)|:        |||  |    ||  ||.\\\\   \\    |
  /\\\\  \\/.    | \\/      ||_____/   )|:  |    |:  ||: \\.   \\\\  |
 |: \\.        | // _____) //      /  \\  |___ |.  ||.  \\    \\. |
 |.  \\    /:  |(:       ||:  __   \\ ( \\_|:  \\|   ||    \\    \\ |
 |___|\\__/|___| \\_______)|__|  \\___) \\_______)\\___)\\___|\\____\\)

Font built from a sig dated 17 Feb 1995
---------------------------------------------------------------
This font has been created using JavE's FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

---

Font modified June 17, 2007 by patorjk 
This was to widen the space character.
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $##
    ___  #
   |"  | #
   ||  | #
   |:  | #
  _|  /  #
 / |_/ ) #
(_____/  #
         ##
  ____ _____  #
 ))_ ")))_ ") #
(____((____(  #
              #
              #
              #
              #
              ##
##
 #
 #
 #
 #
 #
 #
 ##
$#
 #
 #
 #
 #
 #
 #
 ##
  ____  ___   #
 ))_ ")/"  |  #
(____(/  //   #
     /'  /    #
    //  /____ #
   /  //))_ ")#
  |___/(____( #
              ##
&#
 #
 #
 #
 #
 #
 #
 ##
  ____  #
 ))_ ") #
(____(  #
        #
        #
        #
        #
        ##
    ____  #
   /   ") #
  /. __/  #
 // /     #
(: (___   #
 \\     )  #
  \\"__/   #
          ##
  ____    #
 (  " \\   #
  \\__. \\  #
     ) :) #
  __/ //  #
 /"   /   #
(____/    #
          ##
*#
 #
 #
 #
 #
 #
 #
 ##
  _______  #
 ))_    ") #
(_______(  #
 ________  #
 ))_    ") #
(_______(  #
           #
           ##
         #
         #
         #
         #
  _____  #
 //   ") #
(_____/  #
         ##
-#
 #
 #
 #
 #
 #
 #
 ##
         #
         #
         #
         #
  _____  #
 ))_  ") #
(_____(  #
         ##
      ___  #
     /"  | #
    /  //  #
   /'  /   #
  //  /    #
 /  //     #
|___/      #
           ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    ) :) #
(: (____/ //  #
 \\        /   #
  \\"_____/    #
              ##
   ____    #
  /  " \\   #
 /__|| |   #
    |: |   #
   _\\  |   #
  /" \\_|\\  #
 (_______) #
           ##
  _______    #
 /"     "\\   #
(__/\\    :)  #
    / ___/   #
   // \\___   #
  (:  /  "\\  #
   \\_______) #
             ##
  _______  #
 /" __   ) #
(__/ _) ./ #
    /  //  #
 __ \\_ \\\\  #
(: \\__) :\\ #
 \\_______) #
           ##
 ___  ___    #
(: "||_  |   #
|  (__) :|   #
 \\____  ||   #
     _\\ '|   #
    /" \\_|\\  #
   (_______) #
             ##
   ________  #
  /"      ") #
 (:   //\\_/  #
  \\___ \\     #
  __ | \\\\    #
 /" \\/  :)   #
(_______/    #
             ##
    ___     #
   /. ")    #
  /:  /     #
 //  /___   #
(   / _  \\  #
|:   /_) :) #
 \\_______/  #
            ##
 _________  #
("       "\\ #
 \\___/   :/ #
    /   //  #
  __\\  ./   #
 (:  \\_/ \\  #
  \\_______) #
            ##
  _______   #
 /"  _  \\\\  #
|:  _ /  :| #
 \\___/___/  #
 //  /_ \\\\  #
|:  /_   :| #
 \\_______/  #
            ##
  _______    #
 /" _   "\\   #
(: (_/  :|   #
 \\____/ |)   #
    _\\  '|   #
   /" \\__|\\  #
  (________) #
             ##
  ____  #
 ))_ ") #
(____(  #
 _____  #
 ))_ ") #
(____(  #
        #
        ##
   ____  #
  ))_ ") #
 (____(  #
  ____   #
 //  ")  #
(____/   #
         #
         ##
            #
   _______  #
 _/"     ") #
//   /___/  #
\\\\   \\   \\  #
  \\_______) #
            #
            ##
=#
 #
 #
 #
 #
 #
 #
 ##
            #
 _______    #
("     "\\_  #
 \\___\\   \\\\ #
 /   /   // #
(_______/   #
            #
            ##
 ________   #
("      "\\  #
 \\___/   :) #
   /  ___/  #
  //  \\     #
 ('___/     #
  (___)     #
            ##
@#
 #
 #
 #
 #
 #
 #
 ##
      __      #
     /""\\     #
    /    \\    #
   /' /\\  \\   #
  //  __'  \\  #
 /   /  \\\\  \\ #
(___/    \\___)#
              ##
 _______   #
|   _  "\\  #
(. |_)  :) #
|:     \\/  #
(|  _  \\\\  #
|: |_)  :) #
(_______/  #
           ##
  ______   #
 /" _  "\\  #
(: ( \\___) #
 \\/ \\      #
 //  \\ _   #
(:   _) \\  #
 \\_______) #
           ##
 ________   #
|"      "\\  #
(.  ___  :) #
|: \\   ) || #
(| (___\\ || #
|:       :) #
(________/  #
            ##
  _______  #
 /"     "| #
(: ______) #
 \\/    |   #
 // ___)_  #
(:      "| #
 \\_______) #
           ##
  _______  #
 /"     "| #
(: ______) #
 \\/    |   #
 // ___)   #
(:  (      #
 \\__/      #
           ##
  _______   #
 /" _   "|  #
(: ( \\___)  #
 \\/ \\       #
 //  \\ ___  #
(:   _(  _| #
 \\_______)  #
            ##
  __    __   #
 /" |  | "\\  #
(:  (__)  :) #
 \\/      \\/  #
 //  __  \\\\  #
(:  (  )  :) #
 \\__|  |__/  #
             ##
  __     #
 |" \\    #
 ||  |   #
 |:  |   #
 |.  |   #
 /\\  |\\  #
(__\\_|_) #
         ##
      ___  #
     |"  | #
     ||  | #
     |:  | #
  ___|  /  #
 /  :|_/ ) #
(_______/  #
           ##
 __   ___  #
|/"| /  ") #
(: |/   /  #
|    __/   #
(// _  \\   #
|: | \\  \\  #
(__|  \\__) #
           ##
 ___       #
|"  |      #
||  |      #
|:  |      #
 \\  |___   #
( \\_|:  \\  #
 \\_______) #
           ##
 ___      ___ #
|"  \\    /"  |#
 \\   \\  //   |#
 /\\\\  \\/.    |#
|: \\.        |#
|.  \\    /:  |#
|___|\\__/|___|#
              ##
 _____  ___   #
(\\"   \\|"  \\  #
|.\\\\   \\    | #
|: \\.   \\\\  | #
|.  \\    \\. | #
|    \\    \\ | #
 \\___|\\____\\) #
              ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    ) :) #
(: (____/ //  #
 \\        /   #
  \\"_____/    #
              ##
   _______   #
  |   __ "\\  #
  (. |__) :) #
  |:  ____/  #
  (|  /      #
 /|__/ \\     #
(_______)    #
             ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    )  ) #
(: (____/ //  #
 \\         \\  #
  \\"____/\\__\\ #
              ##
  _______   #
 /"      \\  #
|:        | #
|_____/   ) #
 //      /  #
|:  __   \\  #
|__|  \\___) #
            ##
  ________  #
 /"       ) #
(:   \\___/  #
 \\___  \\    #
  __/  \\\\   #
 /" \\   :)  #
(_______/   #
            ##
 ___________  #
("     _   ") #
 )__/  \\\\__/  #
    \\\\_ /     #
    |.  |     #
    \\:  |     #
     \\__|     #
              ##
 ____  ____  #
("  _||_ " | #
|   (  ) : | #
(:  |  | . ) #
 \\\\ \\__/ //  #
 /\\\\ __ //\\  #
(__________) #
             ##
 ___      ___ #
|"  \\    /"  |#
 \\   \\  //  / #
  \\\\  \\/. ./  #
   \\.    //   #
    \\\\   /    #
     \\__/     #
              ##
 __   __  ___ #
|"  |/  \\|  "|#
|'  /    \\:  |#
|: /'        |#
 \\//  /\\'    |#
 /   /  \\\\   |#
|___/    \\___|#
              ##
 ___  ___  #
|"  \\/"  | #
 \\   \\  /  #
  \\\\  \\/   #
  /\\.  \\   #
 /  \\   \\  #
|___/\\___| #
           ##
 ___  ___  #
|"  \\/"  | #
 \\   \\  /  #
  \\\\  \\/   #
  /   /    #
 /   /     #
|___/      #
           ##
 ________   #
("      "\\  #
 \\___/   :) #
   /  ___/  #
  //  \\__   #
 (:   / "\\  #
  \\_______) #
            ##
  ________  #
 /"      ") #
(:   \\___/  #
//   /      #
\\\\   \\___   #
(:   /  "\\  #
 \\________) #
            ##
 ___       #
|  "\\      #
 \\\\  \\     #
  \\  '\\    #
   \\  \\\\   #
    \\\\  \\  #
     \\___| #
           ##
 ________   #
("      "\\  #
 \\___/   :) #
     \\   \\\\ #
  ___/   // #
 /"  \\   :) #
(________/  #
            ##
   __    #
  /""\\   #
 //   \\  #
/'_/\\_\\\\ #
         #
         #
         #
         ##
_#
 #
 #
 #
 #
 #
 #
 ##
 _____   #
("   \\\\  #
 \\_____) #
         #
         #
         #
         #
         ##
      __      #
     /""\\     #
    /    \\    #
   /' /\\  \\   #
  //  __'  \\  #
 /   /  \\\\  \\ #
(___/    \\___)#
              ##
 _______   #
|   _  "\\  #
(. |_)  :) #
|:     \\/  #
(|  _  \\\\  #
|: |_)  :) #
(_______/  #
           ##
  ______   #
 /" _  "\\  #
(: ( \\___) #
 \\/ \\      #
 //  \\ _   #
(:   _) \\  #
 \\_______) #
           ##
 ________   #
|"      "\\  #
(.  ___  :) #
|: \\   ) || #
(| (___\\ || #
|:       :) #
(________/  #
            ##
  _______  #
 /"     "| #
(: ______) #
 \\/    |   #
 // ___)_  #
(:      "| #
 \\_______) #
           ##
  _______  #
 /"     "| #
(: ______) #
 \\/    |   #
 // ___)   #
(:  (      #
 \\__/      #
           ##
  _______   #
 /" _   "|  #
(: ( \\___)  #
 \\/ \\       #
 //  \\ ___  #
(:   _(  _| #
 \\_______)  #
            ##
  __    __   #
 /" |  | "\\  #
(:  (__)  :) #
 \\/      \\/  #
 //  __  \\\\  #
(:  (  )  :) #
 \\__|  |__/  #
             ##
  __     #
 |" \\    #
 ||  |   #
 |:  |   #
 |.  |   #
 /\\  |\\  #
(__\\_|_) #
         ##
      ___  #
     |"  | #
     ||  | #
     |:  | #
  ___|  /  #
 /  :|_/ ) #
(_______/  #
           ##
 __   ___  #
|/"| /  ") #
(: |/   /  #
|    __/   #
(// _  \\   #
|: | \\  \\  #
(__|  \\__) #
           ##
 ___       #
|"  |      #
||  |      #
|:  |      #
 \\  |___   #
( \\_|:  \\  #
 \\_______) #
           ##
 ___      ___ #
|"  \\    /"  |#
 \\   \\  //   |#
 /\\\\  \\/.    |#
|: \\.        |#
|.  \\    /:  |#
|___|\\__/|___|#
              ##
 _____  ___   #
(\\"   \\|"  \\  #
|.\\\\   \\    | #
|: \\.   \\\\  | #
|.  \\    \\. | #
|    \\    \\ | #
 \\___|\\____\\) #
              ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    ) :) #
(: (____/ //  #
 \\        /   #
  \\"_____/    #
              ##
   _______   #
  |   __ "\\  #
  (. |__) :) #
  |:  ____/  #
  (|  /      #
 /|__/ \\     #
(_______)    #
             ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    )  ) #
(: (____/ //  #
 \\         \\  #
  \\"____/\\__\\ #
              ##
  _______   #
 /"      \\  #
|:        | #
|_____/   ) #
 //      /  #
|:  __   \\  #
|__|  \\___) #
            ##
  ________  #
 /"       ) #
(:   \\___/  #
 \\___  \\    #
  __/  \\\\   #
 /" \\   :)  #
(_______/   #
            ##
 ___________  #
("     _   ") #
 )__/  \\\\__/  #
    \\\\_ /     #
    |.  |     #
    \\:  |     #
     \\__|     #
              ##
 ____  ____  #
("  _||_ " | #
|   (  ) : | #
(:  |  | . ) #
 \\\\ \\__/ //  #
 /\\\\ __ //\\  #
(__________) #
             ##
 ___      ___ #
|"  \\    /"  |#
 \\   \\  //  / #
  \\\\  \\/. ./  #
   \\.    //   #
    \\\\   /    #
     \\__/     #
              ##
 __   __  ___ #
|"  |/  \\|  "|#
|'  /    \\:  |#
|: /'        |#
 \\//  /\\'    |#
 /   /  \\\\   |#
|___/    \\___|#
              ##
 ___  ___  #
|"  \\/"  | #
 \\   \\  /  #
  \\\\  \\/   #
  /\\.  \\   #
 /  \\   \\  #
|___/\\___| #
           ##
 ___  ___  #
|"  \\/"  | #
 \\   \\  /  #
  \\\\  \\/   #
  /   /    #
 /   /     #
|___/      #
           ##
 ________   #
("      "\\  #
 \\___/   :) #
   /  ___/  #
  //  \\__   #
 (:   / "\\  #
  \\_______) #
            ##
  ________  #
 /"      ") #
(:   \\___/  #
 \\\\  /      #
 //  \\___   #
(:   /  "\\  #
 \\________) #
            ##
 __   #
|" \\  #
||  | #
|:  | #
|.  | #
|   | #
\\___) #
      ##
 ________   #
("      "\\  #
 \\___/   :) #
     \\  //  #
  ___/  \\\\  #
 /"  \\   :) #
(________/  #
            ##
~#
 #
 #
 #
 #
 #
 #
 ##
      __      #
     /""\\     #
    /    \\    #
   /' /\\  \\   #
  //  __'  \\  #
 /   /  \\\\  \\ #
(___/    \\___)#
              ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    ) :) #
(: (____/ //  #
 \\        /   #
  \\"_____/    #
              ##
 ____  ____  #
("  _||_ " | #
|   (  ) : | #
(:  |  | . ) #
 \\\\ \\__/ //  #
 /\\\\ __ //\\  #
(__________) #
             ##
      __      #
     /""\\     #
    /    \\    #
   /' /\\  \\   #
  //  __'  \\  #
 /   /  \\\\  \\ #
(___/    \\___)#
              ##
    ______    #
   /    " \\   #
  // ____  \\  #
 /  /    ) :) #
(: (____/ //  #
 \\        /   #
  \\"_____/    #
              ##
 ____  ____  #
("  _||_ " | #
|   (  ) : | #
(:  |  | . ) #
 \\\\ \\__/ //  #
 /\\\\ __ //\\  #
(__________) #
             ##
�#
 #
 #
 #
 #
 #
 #
 ##`