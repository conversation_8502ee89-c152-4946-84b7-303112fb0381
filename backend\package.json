{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "boxen": "^8.0.1", "chalk": "^5.4.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "figlet": "^1.8.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "multer": "^2.0.1", "nodemon": "^3.1.10", "sharp": "^0.34.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0"}}