export default `flf2a$ 4 4 20 1 2
serifcap by <PERSON>

$$@
$$@
$$@
$$@@
 _ @
/ \\@
\\_/@
(_)@@
 _ _ @
(_(_)@
     @
     @@
@
@
@
@@
@
@
@
@@
 _  _  @
(_)/ ) @
  / /_ @
 (_/(_)@@
 ___  @
( __) @
/ _) )@
\\___/ @@
 _ @
/_)@
   @
   @@
  _ @
 / )@
( ( @
 \\_)@@
 _  @
( \\ @
 ) )@
(_/ @@
@
@
@
@@
   _   @
 _( )_ @
(_   _)@
  (_)  @@
   @
   @
 _ @
/_)@@
     @
 ___ @
(___)@
     @@
   @
   @
 _ @
(_)@@
   _ @
  / )@
 / / @
(_/  @@
  __  @
 /  \\ @
( () )@
 \\__/ @@
 __ @
(  )@
 )( @
(__)@@
 ___ @
(__ \\@
/ __/@
\\___)@@
 ___ @
(__ )@
 (_ \\@
(___/@@
  __  @
 / ,) @
(_  _)@
  (_) @@
 ___ @
/ __)@
\\__ \\@
(___/@@
  _  @
 / ) @
/ , \\@
\\___/@@
 ___ @
(__ )@
 / / @
(_/  @@
 ___ @
( , )@
/ , \\@
\\___/@@
 ___ @
/ , \\@
\\   /@
 (_/ @@
 _ @
(_)@
 _ @
(_)@@
 _ @
(_)@
 _ @
/_)@@
  _ @
 / )@
( ( @
 \\_)@@
 ___ @
(___)@
 ___ @
(___)@@
 _  @
( \\ @
 ) )@
(_/ @@
 ___ @
(__ )@
 (_/ @
 (_) @@
@
@
@
@@
  __  @
 (  ) @
 /__\\ @
(_)(_)@@
 ___ @
(  ,)@
 ) ,\\@
(___/@@
  __ @
 / _)@
( (_ @
 \\__)@@
 ___  @
(   \\ @
 ) ) )@
(___/ @@
 ___ @
(  _)@
 ) _)@
(___)@@
 ___ @
(  _)@
 ) _)@
(_)  @@
  __ @
 / _)@
( (/\\@
 \\__/@@
 _  _ @
( )( )@
 )__( @
(_)(_)@@
 __ @
(  )@
 )( @
(__)@@
   __ @
  (  )@
 __)( @
(___/ @@
 _ _  @
( ) ) @
 )  \\ @
(_)\\_)@@
 __   @
(  )  @
 )(__ @
(____)@@
 __  __ @
(  \\/  )@
 )    ( @
(_/\\/\\_)@@
 _  _ @
( \\( )@
 )  ( @
(_)\\_)@@
  __  @
 /  \\ @
( () )@
 \\__/ @@
 ___ @
(  ,\\@
 ) _/@
(_)  @@
  __  @
 /  \\ @
( () )@
 \\___\\@@
 ___  @
(  ,) @
 )  \\ @
(_)\\_)@@
 ___ @
/ __)@
\\__ \\@
(___/@@
 ____ @
(_  _)@
  )(  @
 (__) @@
 _  _ @
( )( )@
 )()( @
 \\__/ @@
 _  _ @
( )( )@
 \\\\// @
 (__) @@
 _    _ @
( \\/\\/ )@
 \\    / @
  \\/\\/  @@
 _  _ @
( \\/ )@
 )  ( @
(_/\\_)@@
 _  _ @
( \\/ )@
 \\  / @
(__/  @@
 ___ @
(_  )@
 / / @
(___)@@
@
@
@
@@
 _   @
( \\  @
 \\ \\ @
  \\_)@@
@
@
@
@@
  __  @
 /  \\ @
(_/\\_)@
      @@
     @
     @
 ___ @
(___)@@
 _ @
(_\\@
   @
   @@
  __  @
 (  ) @
 /__\\ @
(_)(_)@@
 ___ @
(  ,)@
 ) ,\\@
(___/@@
  __ @
 / _)@
( (_ @
 \\__)@@
 ___  @
(   \\ @
 ) ) )@
(___/ @@
 ___ @
(  _)@
 ) _)@
(___)@@
 ___ @
(  _)@
 ) _)@
(_)  @@
  __ @
 / _)@
( (/\\@
 \\__/@@
 _  _ @
( )( )@
 )__( @
(_)(_)@@
 __ @
(  )@
 )( @
(__)@@
   __ @
  (  )@
 __)( @
(___/ @@
 _ _  @
( ) ) @
 )  \\ @
(_)\\_)@@
 __   @
(  )  @
 )(__ @
(____)@@
 __  __ @
(  \\/  )@
 )    ( @
(_/\\/\\_)@@
 _  _ @
( \\( )@
 )  ( @
(_)\\_)@@
  __  @
 /  \\ @
( () )@
 \\__/ @@
 ___ @
(  ,\\@
 ) _/@
(_)  @@
  __  @
 /  \\ @
( () )@
 \\___\\@@
 ___  @
(  ,) @
 )  \\ @
(_)\\_)@@
 ___ @
/ __)@
\\__ \\@
(___/@@
 ____ @
(_  _)@
  )(  @
 (__) @@
 _  _ @
( )( )@
 )()( @
 \\__/ @@
 _  _ @
( )( )@
 \\\\// @
 (__) @@
 _    _ @
( \\/\\/ )@
 \\    / @
  \\/\\/  @@
 _  _ @
( \\/ )@
 )  ( @
(_/\\_)@@
 _  _ @
( \\/ )@
 \\  / @
(__/  @@
 ___ @
(_  )@
 / / @
(___)@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
@
@
@
@@
`