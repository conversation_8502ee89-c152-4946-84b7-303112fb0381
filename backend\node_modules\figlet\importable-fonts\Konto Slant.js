export default `flf2a$ 2 2 8 -1 25 0 0 0
Author : <PERSON> <<EMAIL>>
         <PERSON> mark<PERSON>@jave.de
Date   : 2001/9/26 19:57:04
Version: 0.2
-------------------------------------------------
This font is not pure ASCII!

It is intended to work with the characters available
for adding comments to german bank statements.

Those characters are: A..Z 0..9 $%&/\`*�+-,.?^

Font designed by:
  <PERSON> <<EMAIL>>
Converted to FLF by:  
  <PERSON> <<EMAIL>>
-------------------------------------------------
This font has been created using J<PERSON>'s FIGlet font export assistant 
(that will be included in Jave 2.0).

Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

$ #
$ ##
!#
 ##
"#
 ##
##
 ##
$#
 ##
%#
 ##
&#
 ##
'#
 ##
(#
 ##
)#
 ##
*#
 ##
+#
 ##
  #
, ##
-#
 ##
  #
. ##
/#
 ##
0#
 ##
1#
 ##
2#
 ##
3#
 ##
4#
 ##
5#
 ##
6#
 ##
7#
 ##
8#
 ##
9#
 ##
:#
 ##
;#
 ##
<#
 ##
=#
 ##
>#
 ##
?#
 ##
@#
 ##
 /1 #
/�/ ##
 /�1 #
/.I  ##
/\` #
L- ##
 /\`. #
/.-� ##
 /.� #
/..  ##
 /�� #
/�   ##
 /� #
/.T ##
 / / #
/�/  ##
 / #
/  ##
��/ #
L/  ##
 /.� #
/�.  ##
 / #
/. ##
 /1/1 #
/   1 ##
 /1 / #
/ 1/  ##
 /�/ #
/./  ##
 /�/ #
/��  ##
 /�/ #
/.X  ##
 /�/ #
/\`.  ##
 /�� #
../  ##
�/� #
/   ##
/ / #
L/  ##
/ / #
1/  ##
/   / #
L/L/  ##
 1/ #
/1  ##
L/ #
/  ##
��/ #
/.. ##
[#
 ##
\\#
 ##
]#
 ##
^#
 ##
_#
 ##
\`#
 ##
 /1 #
/�/ ##
 /�1 #
/.I  ##
/\` #
L- ##
 /\`. #
/.-� ##
 /.� #
/..  ##
 /�� #
/�   ##
 /� #
/.T ##
 / / #
/�/  ##
 / #
/  ##
��/ #
L/  ##
 /.� #
/�.  ##
 / #
/. ##
 /1/1 #
/   1 ##
 /1 / #
/ 1/  ##
 /�/ #
/./  ##
 /�/ #
/��  ##
 /�/ #
/.X  ##
 /�/ #
/\`.  ##
 /�� #
../  ##
�/� #
/   ##
/ / #
L/  ##
/ / #
1/  ##
/   / #
L/L/  ##
 1/ #
/1  ##
L/ #
/  ##
��/ #
/.. ##
{#
 ##
|#
 ##
}#
 ##
~#
 ##
 /1 #
/�/ ##
 /�/ #
/./  ##
/ / #
L/  ##
 /1 #
/�/ ##
 /�/ #
/./  ##
/ / #
L/  ##
�#
 ##
`