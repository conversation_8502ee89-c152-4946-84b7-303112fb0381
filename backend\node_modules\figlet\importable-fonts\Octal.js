export default `flf2a$ 1 1 11 -1 12 0

octal.flf by <PERSON><PERSON> Wirsing based on
binary.flf (C) 1994 by <PERSON> (<EMAIL>) 94/12/05

figlet 2.1 font, includes ISO Latin-1, excludes "-D" option chars.

Spaces are not shown as binary.  If this is required, change "$@" below
to "20 @".  Change to "@" to remove the hardspace between words.
Note that figlet always removes spaces when it moves words to a new line.

Try option "-m 0" to remove the space between letters (octets).

$@
041 @
042 @
043 @
044 @
045 @
046 @
047 @
050 @
051 @
052 @
053 @
054 @
055 @
056 @
057 @
060 @
061 @
062 @
063 @
064 @
065 @
066 @
067 @
070 @
071 @
072 @
073 @
074 @
075 @
076 @
077 @
100 @
101 @
102 @
103 @
104 @
105 @
106 @
107 @
110 @
111 @
112 @
113 @
114 @
115 @
116 @
117 @
120 @
121 @
122 @
123 @
124 @
125 @
126 @
127 @
130 @
131 @
132 @
133 @
134 @
135 @
136 @
137 @
140 @
141 @
142 @
143 @
144 @
145 @
146 @
147 @
150 @
151 @
152 @
153 @
154 @
155 @
156 @
157 @
160 @
161 @
162 @
163 @
164 @
165 @
166 @
167 @
170 @
171 @
172 @
173 @
174 @
175 @
176 @
 @
 @
 @
 @
 @
 @
 @
127
177 @
128
200 @
129
201 @
130
202 @
131
203 @
132
204 @
133
205 @
134
206 @
135
207 @
136
210 @
137
211 @
138
212 @
139
213 @
140
214 @
141
215 @
142
216 @
143
217 @
144
220 @
145
221 @
146
222 @
147
223 @
148
224 @
149
225 @
150
226 @
151
227 @
152
230 @
153
231 @
154
232 @
155
233 @
156
234 @
157
235 @
158
236 @
159
237 @
160
240 @
161
241 @
162
242 @
163
243 @
164
244 @
165
245 @
166
246 @
167
247 @
168
250 @
169
251 @
170
252 @
171
253 @
172
254 @
173
255 @
174
256 @
175
257 @
176
260 @
177
261 @
178
262 @
179
263 @
180
264 @
181
265 @
182
266 @
183
267 @
184
270 @
185
271 @
186
272 @
187
273 @
188
274 @
189
275 @
190
276 @
191
277 @
192
300 @
193
301 @
194
302 @
195
303 @
196
304 @
197
305 @
198
306 @
199
307 @
200
310 @
201
311 @
202
312 @
203
313 @
204
314 @
205
315 @
206
316 @
207
317 @
208
320 @
209
321 @
210
322 @
211
323 @
212
324 @
213
325 @
214
326 @
215
327 @
216
330 @
217
331 @
218
332 @
219
333 @
220
334 @
221
335 @
222
336 @
223
337 @
224
340 @
225
341 @
226
342 @
227
343 @
228
344 @
229
345 @
230
346 @
231
347 @
232
350 @
233
351 @
234
352 @
235
353 @
236
354 @
237
355 @
238
356 @
239
357 @
240
360 @
241
361 @
242
362 @
243
363 @
244
364 @
245
365 @
246
366 @
247
367 @
248
370 @
249
371 @
250
372 @
251
373 @
252
374 @
253
375 @
254
376 @
255
377 @
`