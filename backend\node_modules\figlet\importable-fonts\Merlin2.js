export default `flf2a$ 9 8 18 0 24 0 64 0
Author : LG Beard
Date   : 2005/5/8 6:45:39
Version: 1.0
-------------------------------------------------
   _               _        _         _          _       _          ~  ~
=_/\\\\___ _____==__/\\\\___==_/\\\\___===_/\\\\_======_/\\\\_===_/\\\\___========~ ~====
(_      v    ))(_  ____))(_   _  ))(_  _))    (____)) (_      ))  ___~____
 /  :   <\\   \\\\ /  ._))   /  |))//  /  \\\\      /  \\\\   /  :   \\\\ ((______))_
/:. |   //   ///:. |____ /:.  ~ \\\\ /:.  \\\\__  /:.  \\\\ /:. |   //  \\:.    /__)
\\___|  //\\  // \\  _____))\\___|  // \\__  ____))\\__  // \\___|  //    \\_  _/
=====\\//==\\//===\\//===========\\//=====\\//========\\//=======\\//=======~~======
      ~    ~     ~             ~       ~          ~         ~
Font Built from a sig dated 17 Feb 1995
-------------------------------------------------
This font has been created using JavE's FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

---

Font modified June 17, 2007 by patorjk 
This was to widen the space character.
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $#
$  $##
   _    #
 _/\\\\_  #
(_  _)) #
 /  \\\\  #
/ \\ :\\\\ #
\\__/\\// #
   \\//  #
        #
        ##
"#
 #
 #
 #
 #
 #
 #
 #
 ##
##
 #
 #
 #
 #
 #
 #
 #
 ##
   _     #
  /\\\\_   #
 /  _ \\\\ #
 \\:.\\\\// #
/\\\\  \\\\  #
\\_  _//  #
  \\//    #
         #
         ##
%#
 #
 #
 #
 #
 #
 #
 #
 ##
&#
 #
 #
 #
 #
 #
 #
 #
 ##
 ____   #
(:. _)) #
  \\//   #
        #
        #
        #
        #
        #
        ##
  _     #
 //\\__  #
//   _\\ #
\\\\:\`\\   #
//:./_  #
\\\\  __/ #
 \\\\/    #
        #
        ##
    _   #
 __/\\\\  #
/_   \\\\ #
  /':// #
 _\\.:\\\\ #
\\__  // #
   \\//  #
        #
        ##
*#
 #
 #
 #
 #
 #
 #
 #
 ##
+#
 #
 #
 #
 #
 #
 #
 #
 ##
       #
       #
       #
       #
  ___  #
((:. ) #
  \\\\/  #
       #
       ##
-#
 #
 #
 #
 #
 #
 #
 #
 ##
        #
        #
        #
        #
   _    #
 _/\\\\_  #
(:.__)) #
        #
        ##
      _  #
     //\\ #
    // / #
   // /  #
 _//./   #
((_ _)   #
  \\\\/    #
         #
         ##
    _      #
 __/\\\\__   #
(_    __)) #
 /  _  \\\\  #
/:.(_)) \\\\ #
\\  _____// #
 \\//       #
           #
           ##
   _    #
 _/\\\\_  #
(____)) #
 /  \\\\  #
/:.  \\\\ #
\\__  // #
   \\//  #
        #
        ##
     _      #
  __//\\     #
 //    \\    #
 \\\\_/  /    #
   /.:/_/\\\\ #
   \\  ___// #
    \\//     #
            #
            ##
    _   #
 __/\\\\  #
/    \\\\ #
\\_/':// #
/ \\.:\\\\ #
\\__  // #
   \\//  #
        #
        ##
   _     #
  /\\\\ _  #
 / ///\\\\ #
 \\ \\/ // #
 _\\:.//  #
(_   _)  #
  \\//    #
         #
         ##
   _      #
  /\\\\__   #
 /   _ \\\\ #
 \\':\\\\\\// #
/ \\  \\\\   #
\\__  //   #
   \\//    #
          #
          ##
   _     #
 _/\\\\_   #
(____))  #
 /  \\_   #
/:. / \\\\ #
\\  ___// #
 \\//     #
         #
         ##
     _   #
 ___/\\\\  #
/     \\\\ #
\\_/ .:// #
  \\  //  #
 (_  _)) #
   \\//   #
         #
         ##
  _     #
 /\\\\__  #
/:.  \\\\ #
\\  \\_// #
/_\\  \\\\ #
\\__.:// #
   \\//  #
        #
        ##
     _   #
 ___/\\\\  #
/     \\\\ #
\\__/ :// #
  \\  //  #
 (_  _)) #
   \\//   #
         #
         ##
 ____   #
(:. _)) #
  \\//   #
        #
   _    #
 _/\\\\_  #
(:.__)) #
        #
        ##
 ____   #
(:. _)) #
  \\//   #
        #
  ___   #
((:. )  #
  \\\\/   #
        #
        ##
        #
  _     #
 //\\__  #
//.  _\\ #
\\\\: __/ #
 \\\\/    #
        #
        #
        ##
=#
 #
 #
 #
 #
 #
 #
 #
 ##
        #
    _   #
 __/\\\\  #
/_  :\\\\ #
\\__ '// #
   \\//  #
        #
        #
        ##
     _   #
 ___/\\\\  #
/     \\\\ #
\\_/\\.:// #
  _/ //  #
 (_  _)) #
   \\//   #
         #
         ##
@#
 #
 #
 #
 #
 #
 #
 #
 ##
    _     #
 __/\\\\__  #
(_  ____) #
 /  _ \\\\  #
/:./_\\ \\\\ #
\\  _   // #
 \\// \\//  #
          #
          ##
    _       #
 __/\\\\___   #
(_     __)) #
 / ._))//   #
/: ._))\\\\   #
\\  ____//   #
 \\//        #
            #
            ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /  ||     #
/:. ||___  #
\\  _____)) #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /   _ \\\\  #
/:. |_\\ \\\\ #
\\  _____// #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /  ._))   #
/:. ||___  #
\\  _____)) #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 / ||__    #
/:. ._))   #
\\  _))     #
 \\//       #
           #
           ##
    _     #
 __/\\\\__  #
(_  ___)) #
 / || _   #
/:. \\/ \\\\ #
\\  ____// #
 \\//      #
          #
          ##
   _        #
 _/\\\\___    #
(_ __ __))  #
 /  |_| \\\\  #
/:.  _   \\\\ #
\\___| |  // #
       \\//  #
            #
            ##
   _    #
 _/\\\\_  #
(____)) #
 /  \\\\  #
/:.  \\\\ #
\\__  // #
   \\//  #
        #
        ##
    _     #
 __/\\\\__  #
(_    _)) #
  \\  \\\\   #
/\\/ .:\\\\  #
\\__  _//  #
   \\//    #
          #
          ##
           #
 _/\\\\___   #
(_    __)) #
 /  : \\\\   #
/:. | //   #
\\___| \\\\   #
     \\//   #
           #
           ##
   _        #
 _/\\\\_      #
(_  _))     #
 /  \\\\      #
/:.  \\\\__   #
\\__  ____)) #
   \\//      #
            #
            ##
   _            #
 _/\\\\___ _____  #
(_      v    )) #
 /  :   <\\   \\\\ #
/:. |   //   // #
\\___|  //\\  //  #
     \\//  \\//   #
                #
                ##
   _       #
 _/\\\\___   #
(_      )) #
 /  :   \\\\ #
/:. |   // #
\\___|  //  #
     \\//   #
           #
           ##
    _      #
 __/\\\\___  #
(_     _)) #
 /  _  \\\\  #
/:.(_)) \\\\ #
\\  _____// #
 \\//       #
           #
           ##
    _       #
  _/\\\\___   #
 (_   _ _)) #
  /  |))\\\\  #
 /:. ___//  #
 \\_ \\\\      #
   \\//      #
            #
            ##
    _        #
 __/\\\\___    #
(_     __))  #
 /  _  \\\\    #
/:.(_)) \\\\_  #
\\  _______// #
 \\//         #
             #
             ##
   _       #
 _/\\\\___   #
(_   _  )) #
 /  |))//  #
/:.    \\\\  #
\\___|  //  #
     \\//   #
           #
           ##
    _     #
   /\\\\__  #
  /    \\\\ #
 _\\  \\_// #
// \\:.\\   #
\\\\__  /   #
   \\\\/    #
          #
          ##
    _     #
 __/\\\\__  #
(__  __)) #
  /  \\\\   #
 /:.  \\\\  #
 \\__  //  #
    \\//   #
          #
          ##
      _    #
 ___ /\\\\   #
/  //\\ \\\\  #
\\:.\\\\_\\ \\\\ #
 \\  :.  // #
(_   ___)) #
  \\//      #
           #
           ##
    _   #
 _ /\\\\  #
/ \\\\ \\\\ #
\\:'/ // #
 \\  //  #
(_  _)) #
  \\//   #
        #
        ##
      _    _    #
 ___ /\\\\  /\\\\   #
/   |  \\\\/  \\\\  #
\\:' |   \\\\   \\\\ #
 \\  :   </   // #
(_   ___^____)) #
  \\//           #
                #
                ##
    _  _    #
  _/\\\\/\\\\_  #
 (_  / __)) #
   \\/ \\\\    #
 __/./.\\\\_  #
(_  _)  _)) #
  \\// \\//   #
            #
            ##
       _  #
  _   /\\\\ #
 /\\\\ / // #
 \\ \\/ //  #
 _\\:.//   #
(_  _))   #
  \\//     #
          #
          ##
    _     #
 __//\\    #
//    \\   #
\\\\_/  /_  #
  /.:/ \\\\ #
  \\  __// #
   \\//    #
          #
          ##
  _     #
 //\\__  #
//   _\\ #
||:\`|   #
||:.|_  #
\\\\  __/ #
 \\\\/    #
        #
        ##
 _       #
/\\\\      #
\\ \\\\     #
 \\ \\\\    #
  \\:\\\\_  #
  (_ _)) #
    \\//  #
         #
         ##
    _   #
 __/\\\\  #
/_   \\\\ #
  |':|| #
 _|.:|| #
\\__  // #
   \\//  #
        #
        ##
^#
 #
 #
 #
 #
 #
 #
 #
 ##
_#
 #
 #
 #
 #
 #
 #
 #
 ##
  ___  #
((:. ) #
  \\\\/  #
       #
       #
       #
       #
       #
       ##
    _     #
 __/\\\\__  #
(_  ____) #
 /  _ \\\\  #
/:./_\\ \\\\ #
\\  _   // #
 \\// \\//  #
          #
          ##
    _       #
 __/\\\\___   #
(_     __)) #
 / ._))//   #
/: ._))\\\\   #
\\  ____//   #
 \\//        #
            #
            ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /  ||     #
/:. ||___  #
\\  _____)) #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /   _ \\\\  #
/:. |_\\ \\\\ #
\\  _____// #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 /  ._))   #
/:. ||___  #
\\  _____)) #
 \\//       #
           #
           ##
    _      #
 __/\\\\___  #
(_  ____)) #
 / ||__    #
/:. ._))   #
\\  _))     #
 \\//       #
           #
           ##
    _     #
 __/\\\\__  #
(_  ___)) #
 / || _   #
/:. \\/ \\\\ #
\\  ____// #
 \\//      #
          #
          ##
   _        #
 _/\\\\___    #
(_ __ __))  #
 /  |_| \\\\  #
/:.  _   \\\\ #
\\___| |  // #
       \\//  #
            #
            ##
   _    #
 _/\\\\_  #
(____)) #
 /  \\\\  #
/:.  \\\\ #
\\__  // #
   \\//  #
        #
        ##
    _     #
 __/\\\\__  #
(_    _)) #
  \\  \\\\   #
/\\/ .:\\\\  #
\\__  _//  #
   \\//    #
          #
          ##
           #
 _/\\\\___   #
(_    __)) #
 /  : \\\\   #
/:. | //   #
\\___| \\\\   #
     \\//   #
           #
           ##
   _        #
 _/\\\\_      #
(_  _))     #
 /  \\\\      #
/:.  \\\\__   #
\\__  ____)) #
   \\//      #
            #
            ##
   _            #
 _/\\\\___ _____  #
(_      v    )) #
 /  :   <\\   \\\\ #
/:. |   //   // #
\\___|  //\\  //  #
     \\//  \\//   #
                #
                ##
   _       #
 _/\\\\___   #
(_      )) #
 /  :   \\\\ #
/:. |   // #
\\___|  //  #
     \\//   #
           #
           ##
    _      #
 __/\\\\___  #
(_     _)) #
 /  _  \\\\  #
/:.(_)) \\\\ #
\\  _____// #
 \\//       #
           #
           ##
    _       #
  _/\\\\___   #
 (_   _ _)) #
  /  |))\\\\  #
 /:. ___//  #
 \\_ \\\\      #
   \\//      #
            #
            ##
    _        #
 __/\\\\___    #
(_     __))  #
 /  _  \\\\    #
/:.(_)) \\\\_  #
\\  _______// #
 \\//         #
             #
             ##
   _       #
 _/\\\\___   #
(_   _  )) #
 /  |))//  #
/:.    \\\\  #
\\___|  //  #
     \\//   #
           #
           ##
    _     #
   /\\\\__  #
  /    \\\\ #
 _\\  \\_// #
// \\:.\\   #
\\\\__  /   #
   \\\\/    #
          #
          ##
    _     #
 __/\\\\__  #
(__  __)) #
  /  \\\\   #
 /:.  \\\\  #
 \\__  //  #
    \\//   #
          #
          ##
      _    #
 ___ /\\\\   #
/  //\\ \\\\  #
\\:.\\\\_\\ \\\\ #
 \\  :.  // #
(_   ___)) #
  \\//      #
           #
           ##
    _   #
 _ /\\\\  #
/ \\\\ \\\\ #
\\:'/ // #
 \\  //  #
(_  _)) #
  \\//   #
        #
        ##
      _    _    #
 ___ /\\\\  /\\\\   #
/   |  \\\\/  \\\\  #
\\:' |   \\\\   \\\\ #
 \\  :   </   // #
(_   ___^____)) #
  \\//           #
                #
                ##
    _  _    #
  _/\\\\/\\\\_  #
 (_  / __)) #
   \\/ \\\\    #
 __/./.\\\\_  #
(_  _)  _)) #
  \\// \\//   #
            #
            ##
       _  #
  _   /\\\\ #
 /\\\\ / // #
 \\ \\/ //  #
 _\\:.//   #
(_  _))   #
  \\//     #
          #
          ##
    _     #
 __//\\    #
//    \\   #
\\\\_/  /_  #
  /.:/ \\\\ #
  \\  __// #
   \\//    #
          #
          ##
  _     #
 //\\__  #
//   _\\ #
\\\\:\`\\   #
//:./_  #
\\\\  __/ #
 \\\\/    #
        #
        ##
   _    #
 _/\\\\_  #
(_  _)) #
  / \\\\  #
 /:. \\\\ #
 \\  _// #
  \\//   #
        #
        ##
    _   #
 __/\\\\  #
/_   \\\\ #
  /':// #
 _\\.:\\\\ #
\\__  // #
   \\//  #
        #
        ##
~#
 #
 #
 #
 #
 #
 #
 #
 ##
    _     #
 __/\\\\__  #
(_  ____) #
 /  _ \\\\  #
/:./_\\ \\\\ #
\\  _   // #
 \\// \\//  #
          #
          ##
    _      #
 __/\\\\___  #
(_     _)) #
 /  _  \\\\  #
/:.(_)) \\\\ #
\\  _____// #
 \\//       #
           #
           ##
      _    #
 ___ /\\\\   #
/  //\\ \\\\  #
\\:.\\\\_\\ \\\\ #
 \\  :.  // #
(_   ___)) #
  \\//      #
           #
           ##
    _     #
 __/\\\\__  #
(_  ____) #
 /  _ \\\\  #
/:./_\\ \\\\ #
\\  _   // #
 \\// \\//  #
          #
          ##
    _      #
 __/\\\\___  #
(_     _)) #
 /  _  \\\\  #
/:.(_)) \\\\ #
\\  _____// #
 \\//       #
           #
           ##
      _    #
 ___ /\\\\   #
/  //\\ \\\\  #
\\:.\\\\_\\ \\\\ #
 \\  :.  // #
(_   ___)) #
  \\//      #
           #
           ##
�#
 #
 #
 #
 #
 #
 #
 #
 ##`