{"name": "boxen", "version": "8.0.1", "description": "Create boxes in the terminal", "license": "MIT", "repository": "sindresorhus/boxen", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["cli", "box", "boxes", "terminal", "term", "console", "ascii", "unicode", "border", "text"], "dependencies": {"ansi-align": "^3.0.1", "camelcase": "^8.0.0", "chalk": "^5.3.0", "cli-boxes": "^3.0.0", "string-width": "^7.2.0", "type-fest": "^4.21.0", "widest-line": "^5.0.0", "wrap-ansi": "^9.0.0"}, "devDependencies": {"ava": "^6.1.3", "nyc": "^17.0.0", "tsd": "^0.31.1", "xo": "^0.58.0"}, "ava": {"snapshotDir": "tests/snapshots", "environmentVariables": {"COLUMNS": "60", "FORCE_COLOR": "0"}}, "xo": {"rules": {"@typescript-eslint/no-unsafe-assignment": "off"}}}