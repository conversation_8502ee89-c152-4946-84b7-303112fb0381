export default `flf2a$ 6 6 12 63 13 0 24511 0
Author :myflix
Date   : 2003/11/6 19:07:12
Version: 1.0
-------------------------------------------------

-------------------------------------------------
This font has been created using JavE's FIGlet font export assistant.
Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.

Changed 2012-05-21: Update to "!" character by patorjk
          #
    o O O #
   o      #
  TS__[O] #
 {======| #
./o--000' ##
    _    #
   | |   #
   |_|   #
  _(_)_  #
_| """ | #
"\`-0-0-' ##
   _ _   #
  ( | )  #
   V V   #
  _____  #
_|     | #
"\`-0-0-' ##
 _| | |_  #
|_  .  _| #
|_     _| #
  |_|_|   #
_|"""""|  #
"\`-0-0-'  ##
   ||_   #
  (_-<   #
  / _/   #
  _||__  #
_|"""""| #
"\`-0-0-' ##
  _  __  #
 (_)/ /  #
   / /_  #
  /_/(_) #
_|"""""| #
"\`-0-0-' ##
  _      #
/ _|___  #
> _|_ _| #
\\_____|  #
_|"""""| #
"\`-0-0-' ##
   (")   #
    \\|   #
         #
  _____  #
_|     | #
"\`-0-0-' ##
   / /   #
  | |    #
  | |    #
  _\\_\\_  #
_|"""""| #
"\`-0-0-' ##
  \\"\\    #
   | |   #
   | |   #
  /_/__  #
_|"""""| #
"\`-0-0-' ##
  _/\\_   #
  >  <   #
   \\/    #
  _____  #
_|     | #
"\`-0-0-' ##
  _|"|_  #
 |_   _| #
   |_|   #
  _____  #
_|     | #
"\`-0-0-' ##
         #
    _    #
   ( )   #
  _|/__  #
_|"""""| #
"\`-0-0-' ##
         #
   ___   #
  |___|  #
  _____  #
_|     | #
"\`-0-0-' ##
         #
         #
    _    #
  _(_)_  #
_|"""""| #
"\`-0-0-' ##
      __ #
     /"/ #
    / /  #
  _/_/_  #
_|"""""| #
"\`-0-0-' ##
    __   #
   /  \\  #
  | () | #
  _\\__/  #
_|"""""| #
"\`-0-0-' ##
    _    #
   / |   #
   | |   #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  |_  )  #
   / /   #
  /___|  #
_|"""""| #
"\`-0-0-' ##
   ____  #
  |__ /  #
   |_ \\  #
  |___/  #
_|"""""| #
"\`-0-0-' ##
  _ _    #
 | | |   #
 |_  _|  #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  | __|  #
  |__ \\  #
  |___/  #
_|"""""| #
"\`-0-0-' ##
    __   #
   / /   #
  / _ \\  #
  \\___/  #
_|"""""| #
"\`-0-0-' ##
   ____  #
  |__  | #
    / /  #
  _/_/_  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  ( _ )  #
  / _ \\  #
  \\___/  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  / _ \\  #
  \\_, /  #
  _/_/_  #
_|"""""| #
"\`-0-0-' ##
    _    #
   (_)   #
    _    #
  _(_)_  #
_|"""""| #
"\`-0-0-' ##
   (_)   #
    _    #
   ( )   #
  _|/__  #
_|"""""| #
"\`-0-0-' ##
    __   #
   / /   #
  < <    #
  _\\_\\_  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  |___|  #
  |___|  #
  _____  #
_|     | #
"\`-0-0-' ##
   __    #
   \\ \\   #
    > >  #
  _/_/_  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  |__ \\  #
    /_/  #
  _(_)_  #
_|"""""| #
"\`-0-0-' ##
 / __ \\  #
/ / _\` | #
\\ \\__,_| #
 \\____/  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  /   \\  #
  | - |  #
  |_|_|  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  | _ )  #
  | _ \\  #
  |___/  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  / __|  #
 | (__   #
  \\___|  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  |   \\  #
  | |) | #
  |___/  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  | __|  #
  | _|   #
  |___|  #
_|"""""| #
"\`-0-0-' ##
    ___  #
   | __| #
   | _|  #
  _|_|_  #
_| """ | #
"\`-0-0-' ##
   ___   #
  / __|  #
 | (_ |  #
  \\___|  #
_|"""""| #
"\`-0-0-' ##
  _  _   #
 | || |  #
 | __ |  #
 |_||_|  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  |_ _|  #
   | |   #
  |___|  #
_|"""""| #
"\`-0-0-' ##
      _  #
   _ | | #
  | || | #
  _\\__/  #
_|"""""| #
"\`-0-0-' ##
  _  __  #
 | |/ /  #
 | ' <   #
 |_|\\_\\  #
_|"""""| #
"\`-0-0-' ##
   _     #
  | |    #
  | |__  #
  |____| #
_|"""""| #
"\`-0-0-' ##
 __  __  #
|  \\/  | #
| |\\/| | #
|_|__|_| #
_|"""""| #
"\`-0-0-' ##
  _  _   #
 | \\| |  #
 | .\` |  #
 |_|\\_|  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  / _ \\  #
 | (_) | #
  \\___/  #
_|"""""| #
"\`-0-0-' ##
    ___  #
   | _ \\ #
   |  _/ #
  _|_|_  #
_| """ | #
"\`-0-0-' ##
  ___    #
 / _ \\   #
| (_) |  #
 \\__\\_\\  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  | _ \\  #
  |   /  #
  |_|_\\  #
_|"""""| #
"\`-0-0-' ##
   ___   #
  / __|  #
  \\__ \\  #
  |___/  #
_|"""""| #
"\`-0-0-' ##
  _____  #
 |_   _| #
   | |   #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
  _   _  #
 | | | | #
 | |_| | #
  \\___/  #
_|"""""| #
"\`-0-0-' ##
 __   __ #
 \\ \\ / / #
  \\ V /  #
  _\\_/_  #
_| """"| #
"\`-0-0-' ##
__      __#
\\ \\    / /#
 \\ \\/\\/ / #
  \\_/\\_/  #
_|"""""|  #
"\`-0-0-'  ##
 __  __  #
 \\ \\/ /  #
  >  <   #
 /_/\\_\\  #
_|"""""| #
"\`-0-0-' ##
 __   __ #
 \\ \\ / / #
  \\ V /  #
  _|_|_  #
_| """ | #
"\`-0-0-' ##
   ____  #
  |_  /  #
   / /   #
  /___|  #
_|"""""| #
"\`-0-0-' ##
  |""|   #
  | |    #
  | |    #
  |__|_  #
_|"""""| #
"\`-0-0-' ##
 __      #
 \\ \\     #
  \\ \\    #
  _\\_\\_  #
_|"""""| #
"\`-0-0-' ##
  |""|   #
   | |   #
   | |   #
  |__|_  #
_|"""""| #
"\`-0-0-' ##
   /\\    #
  |/\\|   #
         #
  _____  #
_|     | #
"\`-0-0-' ##
         #
         #
   ___   #
  |___|  #
_|"""""| #
"\`-0-0-' ##
  (")    #
   \\|    #
         #
  _____  #
_|     | #
"\`-0-0-' ##
         #
  __ _   #
 / _\` |  #
 \\__,_|  #
_|"""""| #
"\`-0-0-' ##
  _      #
 | |__   #
 | '_ \\  #
 |_.__/  #
_|"""""| #
"\`-0-0-' ##
         #
   __    #
  / _|   #
  \\__|_  #
_|"""""| #
"\`-0-0-' ##
     _   #
  __| |  #
 / _\` |  #
 \\__,_|  #
_|"""""| #
"\`-0-0-' ##
         #
   ___   #
  / -_)  #
  \\___|  #
_|"""""| #
"\`-0-0-' ##
     __  #
    / _| #
   |  _| #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
   __ _  #
  / _\` | #
  \\__, | #
  |___/  #
_|"""""| #
"\`-0-0-' ##
  _      #
 | |_    #
 | ' \\   #
 |_||_|  #
_|"""""| #
"\`-0-0-' ##
    _    #
   (_)   #
   | |   #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
    (_)  #
    | |  #
   _/ |  #
  |__/_  #
_|"""""| #
"\`-0-0-' ##
   _     #
  | |__  #
  | / /  #
  |_\\_\\  #
_|"""""| #
"\`-0-0-' ##
    _    #
   | |   #
   | |   #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
         #
  _ __   #
 | '  \\  #
 |_|_|_| #
_|"""""| #
"\`-0-0-' ##
         #
  _ _    #
 | ' \\   #
 |_||_|  #
_|"""""| #
"\`-0-0-' ##
         #
   ___   #
  / _ \\  #
  \\___/  #
_|"""""| #
"\`-0-0-' ##
   _ __  #
  | '_ \\ #
  | .__/ #
  |_|__  #
_|"""""| #
"\`-0-0-' ##
  __ _   #
 / _\` |  #
 \\__, |  #
  __|_|  #
_|"""""| #
"\`-0-0-' ##
         #
    _ _  #
   | '_| #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
         #
   ___   #
  (_-<   #
  /__/_  #
_|"""""| #
"\`-0-0-' ##
   _     #
  | |_   #
  |  _|  #
  _\\__|  #
_|"""""| #
"\`-0-0-' ##
         #
  _  _   #
 | +| |  #
  \\_,_|  #
_|"""""| #
"\`-0-0-' ##
         #
  __ __  #
  \\ V /  #
  _\\_/_  #
_|"""""| #
"\`-0-0-' ##
          #
 __ __ __ #
 \\ V  V / #
  \\_/\\_/  #
_|"""""|  #
"\`-0-0-'  ##
         #
  __ __  #
  \\ \\ /  #
  /_\\_\\  #
_|"""""| #
"\`-0-0-' ##
   _  _  #
  | || | #
   \\_, | #
  _|__/  #
_| """"| #
"\`-0-0-' ##
         #
    ___  #
   |_ /  #
  _/__|  #
_|"""""| #
"\`-0-0-' ##
   /"/   #
 _| |    #
  | |    #
  _\\_\\_  #
_|"""""| #
"\`-0-0-' ##
   |"|   #
   | |   #
   | |   #
  _|_|_  #
_|"""""| #
"\`-0-0-' ##
  \\"\\    #
   | |_  #
   | |   #
  /_/__  #
_|"""""| #
"\`-0-0-' ##
   /\\/|  #
  |/\\/   #
         #
  _____  #
_|     | #
"\`-0-0-' ##
       #
       #
       #
       #
       #
   _ _ ##
�#
 #
 #
 #
 #
 ##
�#
 #
 #
 #
 #
 ##
�#
 #
 #
 #
 #
 ##
�#
 #
 #
 #
 #
 ##
�#
 #
 #
 #
 #
 ##
�#
 #
 #
 #
 #
 ##
`