export default `flf2a$ 6 6 15 -1 20
The elder futhark  -- by <PERSON> <<EMAIL>>
figlet release 2.1 -- 12 Aug 1994
This font consists of the runes in the elder futhark.  It is accurate as
far as I know.  Any corrections, or ideas on improving the font's appearance
are welcome.  Modifying this font is fine with me; please e-mail me the
result.

3 point version of the runes used as uppercase added as lowercase letters
9/22/97 Je<PERSON><PERSON> Pierce <<EMAIL>>

Explanation of first line:
flf2 - "magic number" for file identification
a    - should always be \`a', for now
$    - the "hardblank" -- prints as a blank, but can't be smushed
6    - height of a character
6    - height of a character, not including descenders
15   - max line length (excluding comment lines) + a fudge factor
-1   - default smushmode for this font
20   - number of comment lines

$   $@
$   $@
$ _ $@
$|_|$@
$   $@
$   $@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
      @
__/\\__@
\\    /@
/_  _\\@
  \\/  @
      @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
 _ @
|_|@
 _ @
|_|@
 _ @
|_|@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
   @
 _ @
(_)@
   @
(_)@
   @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
|\\  @
| \\ @
|\\  @
| \\ @
|   @
|   @@
|~\\  @
|  > @
|_/  @
|~\\  @
|  > @
|_/  @@
|    @
|    @
|    @
|\\   @
| \\  @
|  \\ @@
|\\    /| @
| \\  / | @
|  \\/  | @
|  /\\  | @
| /  \\ | @
|/    \\| @@
|\\    /| @
| \\  / | @
|  \\/  | @
|      | @
|      | @
|      | @@
| / / @
|/ /  @
| /   @
|/    @
|     @
|     @@
\\    / @
 \\  /  @
  \\/   @
  /\\   @
 /  \\  @
/    \\ @@
|    | @
|\\   | @
| \\  | @
|  \\ | @
|   \\| @
|    | @@
| @
| @
| @
| @
| @
| @@
 /     @
/      @
\\   \\  @
 \\   \\ @
     / @
    /  @@
  / @
 /  @
/   @
\\   @
 \\  @
  \\ @@
|\\   @
| \\  @
|  \\ @
|    @
|    @
|    @@
|\\  /| @
| \\/ | @
| /\\ | @
|/  \\| @
|    | @
|    | @@
  |   @
\\ |   @
 \\|   @
  |\\  @
  | \\ @
  |   @@
 /~\\  @
/   \\ @
\\   / @
 \\ /  @
 / \\  @
/   \\ @@
|\\  / @
| \\/  @
|     @
|     @
| /\\  @
|/  \\ @@
@
@
@
@
@
@@
|~\\  @
|  \\ @
|  / @
|_/  @
| \\  @
|  \\ @@
  /   @
 /    @
/____ @
    / @
   /  @
  /   @@
  /|\\   @
 / | \\  @
/  |  \\ @
   |    @
   |    @
   |    @@
|\\    @
| \\   @
|  \\  @
|   | @
|   | @
|   | @@
|\\    @
| \\   @
|  \\  @
|   | @
|   | @
|   | @@
|\\  @
| > @
|/  @
|   @
|   @
|   @@
@
@
@
@
@
@@
|~~-__  @
|     | @
|_____| @
|  |  | @
|  |  | @
|  |  | @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
   @
   @
   @
|\\ @
|\\ @
|  @@
   @
   @
   @
|\\ @
|< @
|/ @@
   @
   @
   @
|  @
|  @
|\\ @@
      @
      @
      @
|\\ /| @
| X | @
|/ \\| @@
     @
     @
     @
|\\/| @
|  | @
|  | @@
    @
    @
    @
|// @
|/  @
|   @@
    @
    @
    @
\\ / @
 X  @
/ \\ @@
    @
    @
    @
| | @
|\\| @
| | @@
  @
  @
  @
| @
| @
| @@
    @
    @
    @
/   @
\\ \\ @
  / @@
   @
   @
   @
 / @
<  @
 \\ @@
   @
   @
   @
|\\ @
|  @
|  @@
     @
     @
     @
|\\/| @
|/\\| @
|  | @@
    @
    @
    @
 |  @
\`|  @
 |\` @@
   @
   @
   @
/\\ @
\\/ @
/\\ @@
    @
    @
    @
|\\/ @
|   @
|/\\ @@
@
@
@
@
@
@@
   @
   @
   @
|\\ @
|/ @
|\\ @@
   @
   @
   @
/  @
-- @
 / @@
    @
    @
    @
/|\\ @
 |  @
 |  @@
    @
    @
    @
|\\  @
| | @
| | @@
    @
    @
    @
|\\  @
| | @
| | @@
   @
   @
   @
|\\ @
|/ @
|  @@
@
@
@
@
@
@@
    @
    @
    @
|~\\ @
|_| @
||| @@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
@
@
@
@
@
@@
`