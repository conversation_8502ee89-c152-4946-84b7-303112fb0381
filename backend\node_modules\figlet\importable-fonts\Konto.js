export default `flf2a$ 2 2 8 -1 24 0 0 0
Author : <PERSON> mark<PERSON>@jave.de
Date   : 2001/9/26 19:57:04
Version: 0.2
-------------------------------------------------
This font is NOT pure ASCII!

It is intended to work with the characters available
for adding comments to german bank statements.

Those characters are: A..Z 0..9 $%&/\`*�+-,.?^

People who contributed to this font (in order of appearance):
  <PERSON> <<EMAIL>>
  <PERSON> [<PERSON>] <<EMAIL>>
  <PERSON> <<PERSON><PERSON>@alcatel.de>
  <PERSON> <<PERSON>.<PERSON>@alcatel.de>:
-------------------------------------------------
This font has been created using <PERSON><PERSON>'s FIGlet font export assistant 
(that will be included in Jave 2.0).

Have a look at: http://www.jave.de

Permission is hereby given to modify this font, as long as the
modifier's name is placed on a comment line.
$ #
$ ##
I #
. ##
II #
   ##
##
 ##
$#
 ##
%#
 ##
&#
 ##
'#
 ##
(#
 ##
)#
 ##
*#
 ##
.I. #
 I  ##
  #
, ##
... #
    ##
  #
. ##
/#
 ##
0#
 ##
1#
 ##
2#
 ##
3#
 ##
4#
 ##
5#
 ##
6#
 ##
7#
 ##
8#
 ##
9#
 ##
. #
. ##
. #
, ##
<#
 ##
-- #
-- ##
>#
 ##
?#
 ##
@#
 ##
.^. #
I^I ##
I�D #
I.D ##
,�� #
\`.. ##
I\`. #
I./ ##
I.\` #
I.. ##
I�� #
I�  ##
,�� #
\`.7 ##
L.J #
I I ##
I #
I ##
  7 #
\`.� ##
I,� #
I\`. ##
I   #
L.. ##
IVI #
I I ##
I\`.I #
I \`I ##
,�\`. #
\`..� ##
I��, #
I��  ##
,�\`. #
\`.\`. ##
I��, #
I�\`. ##
/�� #
..7 ##
��T�� #
  I   ##
I  I #
\`..I ##
\` / #
 V  ##
\` ^ / #
 V V  ##
\`.� #
.^. ##
\`./ #
 /  ##
��7 #
/�. ##
[#
 ##
\\#
 ##
]#
 ##
^#
 ##
_#
 ##
\`#
 ##
.^. #
I^I ##
I�D #
I.D ##
,�� #
\`.. ##
I\`. #
I./ ##
I.\` #
I.. ##
I�� #
I�  ##
,�� #
\`.7 ##
L.J #
I I ##
I #
I ##
  7 #
\`.� ##
I,� #
I\`. ##
I   #
L.. ##
IVI #
I I ##
I\`.I #
I \`I ##
,�\`. #
\`..' ##
I��, #
I��  ##
,�\`. #
\`.\`. ##
I��, #
I�\`. ##
/�� #
..7 ##
��T�� #
  I   ##
I  I #
\`..I ##
\` / #
 V  ##
\` ^ / #
 V V  ##
\`.� #
.^. ##
\`./ #
 /  ##
��7 #
/�. ##
{#
 ##
|#
 ##
}#
 ##
~#
 ##
.^. #
I^I ##
,�\`. #
\`..� ##
I  I #
\`..I ##
.^. #
I^I ##
,�\`. #
\`..' ##
I  I #
\`..I ##
�#
 ##`